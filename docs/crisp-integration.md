# Crisp Customer Support Chat Integration

This document outlines the implementation of Crisp customer support chat integration in our Next.js application.

## Overview

The Crisp integration provides:

- **Environment-based availability** - Only active in production environments
- **Authentication-based visibility** - Only available to authenticated users
- **Automatic user identification** when users are authenticated
- **Session continuity** using tokenId-based persistence
- **Custom events** for better customer support context
- **Enhanced plan metadata** tracking for support agents
- **Clean logout handling** with session reset

## Architecture

### Components

1. **CrispProvider** (`src/providers/crisp-provider.tsx`)

   - Initializes Crisp SDK with manual loading
   - Manages user identity and authentication state
   - Handles session continuity with tokenId
   - Integrates with our authentication system

2. **useCrisp Hook** (`src/hooks/use-crisp.ts`)
   - Provides utilities for interacting with Crisp
   - Handles custom event tracking with user context
   - Manages chat widget visibility and state
   - Updates plan metadata automatically

### Configuration

- **Website ID**: `77c4b10d-1fa8-422b-a5cf-372c2a3b29cb`
- **Environment Restriction**: Production only (localhost, staging, dev domains excluded)
- **Authentication Required**: Only available to logged-in users
- **Auth Page Exclusion**: Hidden on signup, signin, forgot-password, etc.
- **Manual Loading**: Enabled (autoload: false)
- **Session Merge**: Enabled for continuity
- **Token-based Sessions**: User ID used as tokenId

## Features

### Environment-based Availability

Crisp chat is only available when:

- Running in production environment (not localhost, staging, or dev domains)
- User is authenticated (logged in)
- Not on authentication pages (signup, signin, forgot-password, etc.)

This ensures that:

- Development/testing environments don't interfere with production support
- Anonymous users cannot access customer support chat
- Authentication flows remain uncluttered

### User Identity Management

When a user is authenticated in production:

- Email and name are automatically set in Crisp
- User ID is used as tokenId for session persistence
- Organization data is included in session metadata
- **Current plan information** (Free/Basic/Premium) is tracked for support agents

### Custom Events

The integration tracks key user actions:

```typescript
// Video generation events
pushEvent(
  'video_generation_started',
  {
    method: 'idea_to_video',
    idea_length: config.idea.length,
    tone: config.tone,
    audience: config.audience,
    platform: config.platform,
  },
  EventsColors.Green
)

// Subscription events
pushEvent(
  'subscription_upgrade_attempted',
  {
    current_plan: 'free',
    target_plan: 'premium',
    source: 'pricing_page',
  },
  EventsColors.Orange
)
```

### Plan Metadata

Support agents can see:

- Current subscription plan (Free, Basic, Premium)
- Organization information
- User authentication status
- Recent user actions and context

### Logout Handling

When users log out:

- Crisp session is reset to clear user data
- Anonymous session is established
- Previous user context is cleared

## Usage Examples

### Basic Chat Controls

```typescript
import { useCrisp } from '@/hooks/use-crisp'

function MyComponent() {
  const { showChat, hideChat, openChat, closeChat, isCrispAvailable } =
    useCrisp()

  // Check if Crisp is available before showing UI
  if (!isCrispAvailable) {
    return null // Don't show chat-related UI in development or for anonymous users
  }

  // Show/hide the chat widget
  const handleShowSupport = () => showChat()
  const handleHideSupport = () => hideChat()

  // Open/close the chat window
  const handleOpenChat = () => openChat()
  const handleCloseChat = () => closeChat()
}
```

### Custom Event Tracking

```typescript
import { useCrisp } from '@/hooks/use-crisp'
import { EventsColors } from 'crisp-sdk-web'

function VideoCreationForm() {
  const { pushEvent } = useCrisp()

  const handleVideoGeneration = () => {
    // Track the event with context
    pushEvent(
      'video_generation_started',
      {
        method: 'text_to_video',
        content_length: text.length,
        voice_selected: selectedVoice,
      },
      EventsColors.Blue
    )
  }
}
```

### Plan Metadata Updates

```typescript
import { useCrisp } from '@/hooks/use-crisp'

function SubscriptionComponent() {
  const { updatePlanMetadata } = useCrisp()

  // Update plan info after subscription change
  useEffect(() => {
    if (subscriptionChanged) {
      updatePlanMetadata()
    }
  }, [subscriptionChanged])
}
```

## Integration Points

The Crisp integration is active in:

1. **App Layout** - CrispProvider wraps the entire application
2. **Video Creation Forms** - Tracks generation events with context
3. **Billing/Subscription** - Tracks upgrade attempts and plan changes
4. **User Authentication** - Handles login/logout identity management
5. **Startup Flow** - Initializes with existing authenticated sessions

## Benefits for Customer Support

1. **Rich Context**: Support agents see user's plan, recent actions, and technical details
2. **Session Continuity**: Conversations persist across browser sessions
3. **Automatic Identification**: No need for users to re-identify themselves
4. **Event Timeline**: Clear history of user actions leading to support requests
5. **Plan-aware Support**: Agents immediately see subscription level (Free/Basic/Premium) and limitations
6. **Production-only Support**: No confusion from development/testing environments
7. **Authenticated Users Only**: Ensures all support requests come from legitimate users

## Technical Notes

- Crisp is initialized only once per session
- All errors are caught and logged without breaking the app
- Events include automatic user and organization context
- Manual loading prevents chat from appearing before user identity is set
- TypeScript definitions are included for type safety

## Future Enhancements

Potential improvements:

- Add more granular event tracking
- Implement custom chat triggers based on user behavior
- Add help desk integration for self-service
- Create custom chat commands for common support tasks
- Implement proactive chat based on user journey stage
