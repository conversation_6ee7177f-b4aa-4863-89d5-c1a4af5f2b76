import React from 'react'
import { Check, X } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { Plan } from './types'
import {
  useSubscription,
  useCachedActiveSubscription,
} from '@/hooks/useSubscription'
import { toast } from 'sonner'
import { getBillingPeriod } from '@/lib/subscription-utils'
import { useCrisp } from '@/hooks/use-crisp'
import { EventsColors } from 'crisp-sdk-web'

interface PricingTableProps {
  plans: Plan[]
  billingPeriod: 'monthly' | 'annual'
}

const FeatureItem = ({
  children,
  active = true,
}: {
  children: React.ReactNode
  active?: boolean
}) => (
  <div className='py-1.5 flex items-start gap-2.5 text-sm'>
    {active ? (
      <div className='h-4 w-4 mt-0.5 flex items-center justify-center shrink-0'>
        <Check className='h-3.5 w-3.5 text-green-500' />
      </div>
    ) : (
      <div className='h-4 w-4 mt-0.5 flex items-center justify-center shrink-0'>
        <X className='h-3.5 w-3.5 text-red-500' />
      </div>
    )}
    <span className='text-card-foreground'>{children}</span>
  </div>
)

const PricingTable: React.FC<PricingTableProps> = ({
  plans,
  billingPeriod,
}) => {
  const { upgradeSubscription, canUpgradeSubscription, isLoading } =
    useSubscription()
  const { subscription } = useCachedActiveSubscription()
  const { pushEvent } = useCrisp()

  // Helper function to get button text based on current subscription and plan
  const getButtonText = (planName: string) => {
    if (!subscription) {
      // No subscription - Free plan shows "Current Plan", others show "Get Started"
      return planName === 'Free' ? 'Current Plan' : 'Get Started'
    }

    const currentPlan = subscription.plan
    const currentBillingPeriod = getBillingPeriod(
      subscription.periodStart,
      subscription.periodEnd
    )

    // Extract base plan name for comparison (remove -monthly or -annual suffix)
    const currentPlanBase = currentPlan.replace(/-monthly|-annual$/, '')
    const planNameBase = planName // planName is already the display name (Basic, Premium, Free)

    const isCurrentPlan =
      currentPlanBase.toLowerCase() === planNameBase.toLowerCase()
    const isCurrentBillingPeriod = currentBillingPeriod === billingPeriod

    // Only show "Current Plan" if both plan name and billing period match
    if (isCurrentPlan && isCurrentBillingPeriod) {
      return 'Current Plan'
    } else if (planName === 'Free') {
      return 'Cancel Plan'
    } else {
      return 'Switch Plan'
    }
  }

  // Helper function to determine if button should be disabled
  const isButtonDisabled = (planName: string) => {
    if (isLoading) return true
    if (!subscription) {
      return planName === 'Free' // Disable Free button when no subscription
    }
    const currentPlan = subscription.plan
    const currentBillingPeriod = getBillingPeriod(
      subscription.periodStart,
      subscription.periodEnd
    )

    // Extract base plan name for comparison (remove -monthly or -annual suffix)
    const currentPlanBase = currentPlan.replace(/-monthly|-annual$/, '')
    const planNameBase = planName // planName is already the display name (Basic, Premium, Free)

    const isCurrentPlan =
      currentPlanBase.toLowerCase() === planNameBase.toLowerCase()
    const isCurrentBillingPeriod = currentBillingPeriod === billingPeriod

    // Only disable if both plan name and billing period match
    return isCurrentPlan && isCurrentBillingPeriod
  }

  // Filter to only show Free, Basic, and Premium plans
  const filteredPlans = plans.filter(
    plan =>
      plan.name === 'Free' || plan.name === 'Basic' || plan.name === 'Premium'
  )

  const handleGetStarted = async (planName: string) => {
    const buttonText = getButtonText(planName)

    if (buttonText === 'Current Plan') {
      toast.info(`You are already on the ${planName} plan`)
      return
    }

    if (buttonText === 'Cancel Plan') {
      // Handle cancellation - this should redirect to subscription management
      toast.info('Please go to the Subscription tab to cancel your plan')
      return
    }

    // Handle upgrade or switch
    if (planName === 'Free') {
      toast.info('You are already on the free plan')
      return
    }

    // Check permissions before attempting upgrade
    const permissionCheck = await canUpgradeSubscription()
    if (!permissionCheck.canUpgrade) {
      toast.error(
        permissionCheck.error ||
          "You don't have permission to upgrade the subscription"
      )
      return
    }

    // Track upgrade button clicked from pricing page
    const currentPlanBase =
      subscription?.plan?.replace(/-monthly|-annual$/, '') ?? 'Free'
    const currentBillingPeriod = subscription
      ? getBillingPeriod(subscription.periodStart, subscription.periodEnd)
      : undefined

    // Dynamic PostHog import and tracking
    try {
      const posthog = await import('posthog-js')
      posthog.default.capture('upgrade_button_clicked', {
        source: 'pricing_page',
        current_plan: currentPlanBase,
        current_billing_period: currentBillingPeriod,
        target_plan: planName,
        target_billing_period: billingPeriod,
      })
    } catch (error) {
      console.error('PostHog tracking failed:', error)
    }

    // Push Crisp event for customer support context
    pushEvent(
      'subscription_upgrade_attempted',
      {
        current_plan: currentPlanBase,
        current_billing_period: currentBillingPeriod,
        target_plan: planName,
        target_billing_period: billingPeriod,
        source: 'pricing_page',
      },
      EventsColors.Orange
    )

    // Convert display name to lowercase for subscription upgrade
    const basePlanName = planName.toLowerCase() as 'basic' | 'premium'
    const annual = billingPeriod === 'annual'

    const result = await upgradeSubscription({
      plan: basePlanName,
      annual,
      successUrl: '/billing/subscription',
      cancelUrl: '/billing/plans',
    })

    if (result?.error) {
      console.error('Subscription upgrade failed:', result.error)
    }
  }

  return (
    <div className='flex flex-wrap justify-center gap-4 sm:gap-6 w-full max-w-7xl mx-auto px-4 sm:px-0'>
      {filteredPlans.map((plan, index) => {
        const isBasic = plan.name === 'Basic'
        const isFree = plan.name === 'Free'
        const displayPrice =
          billingPeriod === 'annual' && plan.annualPrice
            ? plan.annualPrice
            : plan.price

        return (
          <div
            key={index}
            className={cn(
              'flex flex-col bg-card text-card-foreground rounded-lg border border-border shadow-sm',
              'w-full sm:w-auto sm:min-w-[280px] sm:max-w-[350px] lg:max-w-[380px]',
              'min-h-[600px] sm:min-h-[700px]',
              'flex-shrink-0',
              isBasic && 'ring-2 ring-primary ring-offset-2'
            )}
          >
            {/* Plan Header */}
            <div className='px-6 pt-8 pb-6 text-center'>
              <h3 className='text-2xl font-bold text-card-foreground mb-2'>
                {plan.name}
              </h3>
              <div className='flex items-baseline justify-center gap-1 mb-3'>
                <span className='text-4xl font-bold text-card-foreground'>
                  {displayPrice}
                </span>
                <span className='text-lg text-muted-foreground'>
                  {isFree
                    ? ''
                    : billingPeriod === 'annual'
                      ? '/year'
                      : '/month'}
                </span>
              </div>
              <p className='text-sm text-muted-foreground mb-6'>
                {plan.name === 'Free' && 'For individuals'}
                {plan.name === 'Basic' &&
                  'For small to medium-sized businesses'}
                {plan.name === 'Premium' && 'For growing businesses and teams'}
              </p>

              {/* CTA Button */}
              <Button
                className={cn(
                  'w-full py-3 text-base font-medium bg-muted hover:bg-muted/80 text-card-foreground border border-border',
                  isBasic
                    ? 'bg-primary hover:bg-primary/90 text-primary-foreground border-primary'
                    : ''
                )}
                onClick={() => handleGetStarted(plan.name)}
                disabled={isButtonDisabled(plan.name)}
              >
                {isLoading ? 'Loading...' : getButtonText(plan.name)}
              </Button>
            </div>

            {/* Features */}
            <div className='px-6  flex-grow'>
              {/* Usage Section */}
              <div className='mb-2'>
                {/* <div className='flex items-center gap-2 mb-3'>
                  <ChartColumn className='h-4 w-4 text-muted-foreground' />
                  <h4 className='font-semibold text-card-foreground'>Usage</h4>
                </div> */}
                <div className='space-y-2'>
                  {plan.name === 'Free' && (
                    <>
                      <FeatureItem>{plan.videoCreation} projects</FeatureItem>
                      <FeatureItem active={false}>
                        {plan.videoDownloads} video exports
                      </FeatureItem>
                      <FeatureItem>{plan.aiImageGenerator}</FeatureItem>
                      <FeatureItem>{plan.storage} storage</FeatureItem>
                    </>
                  )}
                  {plan.name === 'Basic' && (
                    <>
                      <FeatureItem>{plan.videoCreation} projects</FeatureItem>
                      <FeatureItem>
                        {plan.videoDownloads} video exports
                      </FeatureItem>
                      <FeatureItem>{plan.aiImageGenerator}</FeatureItem>
                      <FeatureItem>{plan.storage} storage</FeatureItem>
                    </>
                  )}
                  {plan.name === 'Premium' && (
                    <>
                      <FeatureItem>{plan.videoCreation} projects</FeatureItem>
                      <FeatureItem>
                        {plan.videoDownloads} video exports
                      </FeatureItem>
                      <FeatureItem>{plan.aiImageGenerator}</FeatureItem>
                      <FeatureItem>{plan.storage} storage</FeatureItem>
                    </>
                  )}
                </div>
              </div>

              {/* Features Section */}
              <div className='mb-0'>
                {/* <div className='flex items-center gap-2 mb-3'>
                  <Shield className='h-4 w-4 text-muted-foreground' />
                  <h4 className='font-semibold text-card-foreground'>
                    Features
                  </h4>
                </div> */}
                <div className='space-y-2'>
                  {plan.name === 'Free' && (
                    <>
                      <FeatureItem>1 team member</FeatureItem>
                      <FeatureItem>
                        3 voice regenerations per project
                      </FeatureItem>
                      <FeatureItem>1 min video script limit</FeatureItem>
                      <FeatureItem>upto 10 min Podcast</FeatureItem>
                      <FeatureItem active={false}>Pro voices</FeatureItem>
                      <FeatureItem active={false}>
                        720p video resolution
                      </FeatureItem>
                      <FeatureItem active={false}>
                        Bulk voice regeneration
                      </FeatureItem>
                      <FeatureItem active={false}>
                        Publish to Youtube
                      </FeatureItem>
                    </>
                  )}
                  {plan.name === 'Basic' && (
                    <>
                      <FeatureItem>1 team member</FeatureItem>
                      <FeatureItem>
                        10 voice regenerations per project
                      </FeatureItem>
                      <FeatureItem>3 min video script limit</FeatureItem>
                      <FeatureItem>upto 30 minutes Podcast</FeatureItem>
                      <FeatureItem>Pro voices</FeatureItem>
                      <FeatureItem>720p video resolution</FeatureItem>
                      <FeatureItem active={false}>
                        Bulk voice regeneration
                      </FeatureItem>
                      <FeatureItem active={false}>
                        Publish to Youtube
                      </FeatureItem>
                    </>
                  )}
                  {plan.name === 'Premium' && (
                    <>
                      <FeatureItem>5 team members</FeatureItem>
                      <FeatureItem>
                        20 voice regenerations per project
                      </FeatureItem>
                      <FeatureItem>5 min video script limit</FeatureItem>
                      <FeatureItem>upto 80 minutes Podcast</FeatureItem>
                      <FeatureItem>Pro voices</FeatureItem>
                      <FeatureItem>1080p video resolution</FeatureItem>
                      <FeatureItem>Bulk voice regeneration</FeatureItem>
                      <FeatureItem>Publish to Youtube</FeatureItem>
                    </>
                  )}
                </div>
              </div>

              {/* Support Section */}
              <div className='mb-6'>
                {/* <div className='flex items-center gap-2 mb-3'>
                  <HeadphonesIcon className='h-4 w-4 text-muted-foreground' />
                  <h4 className='font-semibold text-card-foreground'>
                    Support
                  </h4>
                </div> */}
                <div className='space-y-2'>
                  {plan.name === 'Free' && (
                    <>
                      <FeatureItem>Chat Support</FeatureItem>
                    </>
                  )}
                  {plan.name === 'Basic' && (
                    <>
                      <FeatureItem>Chat Support</FeatureItem>
                    </>
                  )}
                  {plan.name === 'Premium' && (
                    <>
                      <FeatureItem>Chat Support</FeatureItem>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export { PricingTable }
