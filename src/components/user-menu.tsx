'use client'

import { Lo<PERSON><PERSON><PERSON>, User, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Check } from 'lucide-react'
import { authClient } from '@/lib/auth-client'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { useTheme } from 'next-themes'
import * as React from 'react'
import { Crisp } from 'crisp-sdk-web'

export function UserMenu() {
  const router = useRouter()
  const { data: session, isPending } = authClient.useSession()
  const { resolvedTheme, setTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  // Only show the theme toggle after mounting to avoid hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  const themeOptions = [
    { value: 'light', label: 'Light', icon: Sun },
    { value: 'dark', label: 'Dark', icon: Moon },
    { value: 'system', label: 'System', icon: Laptop },
  ]

  const currentTheme =
    themeOptions.find(t => {
      if (t.value === resolvedTheme) return true
      if (t.value === 'system' && !resolvedTheme) return true
      return false
    }) || themeOptions[0]

  const handleLogout = async () => {
    // Reset Crisp session to clear user data
    if (typeof window !== 'undefined') {
      try {
        Crisp.session.reset(false) // Don't reload the page
        console.log('Crisp: Session reset on logout')
      } catch (error) {
        console.error('Crisp: Error resetting session on logout', error)
      }
    }

    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          router.push('/signin')
        },
      },
    })
  }

  if (isPending) {
    return (
      <Button size='sm' className='h-8 w-8 md:h-7 md:w-7 rounded-full '>
        <Avatar className='h-6 w-6 md:h-7 md:w-7'>
          <AvatarFallback className='text-xs bg-[#6378d6] text-white'>
            ...
          </AvatarFallback>
        </Avatar>
      </Button>
    )
  }

  if (!session?.user) {
    return (
      <Button
        variant='ghost'
        size='sm'
        className='h-8 w-8 md:h-7 md:w-7 rounded-full'
      >
        <Avatar className='h-6 w-6 md:h-7 md:w-7'>
          <AvatarFallback className='text-xs'>
            <User className='h-3 w-3' />
          </AvatarFallback>
        </Avatar>
      </Button>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='sm'
          className='h-8 w-8 md:h-7 md:w-7 rounded-full p-0'
        >
          <Avatar className='h-6 w-6 md:h-7 md:w-7'>
            <AvatarImage
              src={session?.user?.image || undefined}
              alt={session?.user?.name || ''}
            />
            <AvatarFallback className='text-xs text-white'>
              {session?.user?.name?.charAt(0)?.toUpperCase()}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className='w-56 rounded-lg'
        align='end'
        sideOffset={8}
      >
        <DropdownMenuLabel className='p-0 font-normal'>
          <div className='flex items-center gap-2 px-1 py-1.5 text-left text-sm'>
            <Avatar className='h-8 w-8 rounded-lg'>
              <AvatarImage
                src={session?.user?.image || undefined}
                alt={session?.user?.name || ''}
              />
              <AvatarFallback className='rounded-lg'>
                {session?.user?.name?.charAt(0)?.toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className='grid flex-1 text-left text-sm leading-tight'>
              <span className='truncate font-semibold'>
                {session?.user?.name}
              </span>
              <span className='truncate text-xs'>{session?.user?.email}</span>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem
            onClick={() => {
              // For now, we'll redirect to settings page
              // You can implement a proper user profile modal later
              router.push('/settings')
            }}
          >
            <User className='mr-2 size-4' />
            Manage account
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        {mounted && (
          <>
            <DropdownMenuLabel className='px-2 py-1.5 text-xs font-medium text-muted-foreground'>
              Theme
            </DropdownMenuLabel>
            {themeOptions.map(option => (
              <DropdownMenuItem
                key={option.value}
                onClick={() => setTheme(option.value)}
                className='flex justify-between px-2 py-1.5'
              >
                <div className='flex items-center'>
                  <option.icon className='mr-2 h-4 w-4' />
                  <span className='text-sm'>{option.label}</span>
                </div>
                {currentTheme.value === option.value && (
                  <Check className='h-4 w-4 ml-2' />
                )}
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
          </>
        )}
        <DropdownMenuItem onClick={() => handleLogout()}>
          <LogOut className='mr-2 size-4' />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
