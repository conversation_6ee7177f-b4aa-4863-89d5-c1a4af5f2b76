'use client'

import { Crisp, EventsColors } from 'crisp-sdk-web'
import { authClient } from '@/lib/auth-client'
import { usePlanLimits } from '@/hooks/use-feature-gating'

/**
 * Hook for interacting with Crisp customer support chat
 * Provides utilities for sending custom events and managing chat state
 */
export function useCrisp() {
  const { data: session } = authClient.useSession()
  const { data: activeOrganization } = authClient.useActiveOrganization()
  const { planName } = usePlanLimits()

  /**
   * Push a custom event to Crisp with user context
   */
  const pushEvent = (
    eventName: string,
    eventData?: Record<string, any>,
    color: EventsColors = EventsColors.Blue
  ) => {
    try {
      const enrichedData = {
        ...eventData,
        user_id: session?.user?.id,
        user_email: session?.user?.email,
        organization_id: activeOrganization?.id,
        organization_name: activeOrganization?.name,
        plan: planName,
        timestamp: new Date().toISOString(),
      }

      Crisp.session.pushEvent(eventName, enrichedData, color)
      console.log('Crisp: Event pushed', eventName, enrichedData)
    } catch (error) {
      console.error('Crisp: Failed to push event', error)
      // Don't throw error to avoid breaking the app
    }
  }

  /**
   * Update user plan metadata in Crisp
   */
  const updatePlanMetadata = () => {
    try {
      const planData = {
        current_plan: planName,
        plan_updated_at: new Date().toISOString(),
      }

      Crisp.session.setData(planData)
      console.log('Crisp: Plan metadata updated', planData)
    } catch (error) {
      console.error('Crisp: Failed to update plan metadata', error)
    }
  }

  /**
   * Show the chat widget
   */
  const showChat = () => {
    try {
      Crisp.chat.show()
    } catch (error) {
      console.error('Crisp: Failed to show chat', error)
    }
  }

  /**
   * Hide the chat widget
   */
  const hideChat = () => {
    try {
      Crisp.chat.hide()
    } catch (error) {
      console.error('Crisp: Failed to hide chat', error)
    }
  }

  /**
   * Open the chat widget
   */
  const openChat = () => {
    try {
      Crisp.chat.open()
    } catch (error) {
      console.error('Crisp: Failed to open chat', error)
    }
  }

  /**
   * Close the chat widget
   */
  const closeChat = () => {
    try {
      Crisp.chat.close()
    } catch (error) {
      console.error('Crisp: Failed to close chat', error)
    }
  }

  /**
   * Get unread message count
   */
  const getUnreadCount = (): number => {
    try {
      return Crisp.chat.unreadCount()
    } catch (error) {
      console.error('Crisp: Failed to get unread count', error)
      return 0
    }
  }

  /**
   * Check if chat is opened
   */
  const isChatOpened = (): boolean => {
    try {
      return Crisp.chat.isChatOpened()
    } catch (error) {
      console.error('Crisp: Failed to check if chat is opened', error)
      return false
    }
  }

  /**
   * Check if chat is visible
   */
  const isChatVisible = (): boolean => {
    try {
      return Crisp.chat.isVisible()
    } catch (error) {
      console.error('Crisp: Failed to check if chat is visible', error)
      return false
    }
  }

  return {
    pushEvent,
    updatePlanMetadata,
    showChat,
    hideChat,
    openChat,
    closeChat,
    getUnreadCount,
    isChatOpened,
    isChatVisible,
  }
}
