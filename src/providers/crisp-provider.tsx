'use client'

import React from 'react'
import { Crisp } from 'crisp-sdk-web'
import { authClient } from '@/lib/auth-client'

const CRISP_WEBSITE_ID = '77c4b10d-1fa8-422b-a5cf-372c2a3b29cb'

/**
 * Crisp customer support chat provider that integrates with our authentication system
 * and manages chat state throughout the user session
 */
export function CrispProvider({ children }: { children: React.ReactNode }) {
  const { data: session, isPending: sessionLoading } = authClient.useSession()
  const { data: activeOrganization } = authClient.useActiveOrganization()
  const [isInitialized, setIsInitialized] = React.useState(false)

  // Initialize Crisp on mount
  React.useEffect(() => {
    if (typeof window === 'undefined' || isInitialized) return

    try {
      // Configure Crisp with manual loading
      Crisp.configure(CRISP_WEBSITE_ID, {
        autoload: false, // Manual loading to control when chat appears
        sessionMerge: true, // Enable session continuity
      })

      // Load Crisp
      Crisp.load()
      setIsInitialized(true)
      console.log('Crisp: Initialized successfully')
    } catch (error) {
      console.error('Crisp: Failed to initialize', error)
      // Don't throw error to avoid breaking the app
    }
  }, [isInitialized])

  // Handle user authentication and identity management
  React.useEffect(() => {
    if (!isInitialized || sessionLoading || typeof window === 'undefined') return

    try {
      if (session?.user) {
        // Set user identity when authenticated
        if (session.user.email) {
          Crisp.user.setEmail(session.user.email)
        }
        
        if (session.user.name) {
          Crisp.user.setNickname(session.user.name)
        }

        // Set session token for continuity
        if (session.user.id) {
          Crisp.setTokenId(session.user.id)
        }

        // Add user metadata
        const userData: Record<string, any> = {
          user_id: session.user.id,
          authenticated: true,
        }

        // Add organization data if available
        if (activeOrganization) {
          userData.organization_id = activeOrganization.id
          userData.organization_name = activeOrganization.name
        }

        Crisp.session.setData(userData)

        console.log('Crisp: User identity set', session.user.email)
      } else {
        // Clear user data when not authenticated
        Crisp.user.setEmail('')
        Crisp.user.setNickname('')
        Crisp.setTokenId()
        
        // Set anonymous user data
        Crisp.session.setData({
          authenticated: false,
        })

        console.log('Crisp: User identity cleared (anonymous)')
      }
    } catch (error) {
      console.error('Crisp: Failed to set user identity', error)
      // Don't throw error to avoid breaking the app
    }
  }, [isInitialized, session, sessionLoading, activeOrganization])

  // This provider doesn't render anything, just manages Crisp state
  return <>{children}</>
}
